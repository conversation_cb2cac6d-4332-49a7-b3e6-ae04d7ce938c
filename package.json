{"name": "organiser-remake", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.8", "@react-google-maps/api": "^2.20.6", "@tailwindcss/vite": "^4.0.12", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html-to-image": "^1.11.13", "input-otp": "^1.4.2", "lucide-react": "^0.479.0", "motion": "^12.10.2", "next-themes": "^0.4.6", "qrcode": "^1.5.4", "quill": "^2.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-router-dom": "^7.3.0", "recharts": "^2.15.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.12", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.13.10", "@types/qrcode": "^1.5.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jspdf": "^3.0.1", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}